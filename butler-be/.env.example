# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/butler

# JWT
JWT_SECRET=your_jwt_secret_key

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Razorpay
KEY_ID=your_razorpay_key_id
KEY_SECRET=your_razorpay_key_secret

# Groq (Primary AI Service)
GROQ_API_KEY=your_groq_api_key

# Optional: For advanced vector embeddings (if needed)
# OPENAI_API_KEY=your_openai_api_key

# Campaign Settings
ENABLE_CAMPAIGNS=true

# Atlas Vector Search Configuration
ENABLE_ATLAS_VECTOR_SEARCH=true
ENABLE_HYBRID_SEARCH=true
ENABLE_VECTOR_PREFILTERING=true
FALLBACK_TO_TEXT_SEARCH=true

# Atlas Vector Search Performance
ATLAS_VECTOR_DEFAULT_LIMIT=10
ATLAS_VECTOR_MIN_SCORE=0.05
ATLAS_VECTOR_WEIGHT=0.7
ATLAS_VECTOR_TIMEOUT_MS=5000
ATLAS_VECTOR_CACHE_TTL=300

# Atlas Vector Search Monitoring
LOG_VECTOR_SEARCHES=false
LOG_VECTOR_PERFORMANCE=false
TRACK_VECTOR_ANALYTICS=false
